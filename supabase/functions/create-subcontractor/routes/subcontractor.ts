import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { SubcontractorService } from "../services/subcontractor.service.ts";
import { env } from "../../_shared/env.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho_api/api/zoho.ts";
import { SubcontractorsService } from "../../zoho_api/api/modules/subcontractors/subcontractors.ts";

// Helper function to convert files to email attachments
async function convertFilesToAttachments(files: { [key: string]: File | File[] }) {
  const attachments: Array<{
    content: string;
    filename: string;
    type: string;
    disposition: string;
  }> = [];

  for (const [fieldName, fileOrFiles] of Object.entries(files)) {
    // Handle both single files and arrays of files
    const filesToProcess = Array.isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];

    for (const file of filesToProcess) {
      if (file && file.size > 0) {
        // Convert file to base64
        const arrayBuffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const base64String = btoa(String.fromCharCode(...uint8Array));

        attachments.push({
          content: base64String,
          filename: file.name || `${fieldName}.${getFileExtension(file.type)}`,
          type: file.type || 'application/octet-stream',
          disposition: 'attachment'
        });
      }
    }
  }

  return attachments;
}

// Helper function to get file extension from MIME type
function getFileExtension(mimeType: string): string {
  const mimeToExt: { [key: string]: string } = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'text/plain': 'txt'
  };

  return mimeToExt[mimeType] || 'bin';
}

type SubcontractorRequest = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  hearAboutUs?: string;
  contactConsent?: boolean;
  yearsExperience: number;
  travelDistance: number;
  additionalQualifications?: string;
  referred?: string;
  referrerName?: string;
  
  // Boolean fields
  gasRegistered: boolean;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;
  
  // Work type and preferences
  workType?: string;
  preferredWorkType?: string | string[];
  availableDays?: string[];
};

export async function handleCreateSubcontractor(context: Context) {
  try {
    let body: SubcontractorRequest;
    let files: { [key: string]: File | File[] } = {};

    // Check content type and parse accordingly
    const contentType = context.request.headers.get("content-type") || "";

    if (contentType.includes("multipart/form-data")) {
      // Handle multipart form data (with files)
      const formData = await context.request.body({ type: "form-data" }).value;

      // Parse form fields
      const formFields: any = {};
      const fileFields: { [key: string]: File | File[] } = {};

      // First, collect all values for each key to handle multiple values
      const formFieldsMap = new Map<string, string[]>();
      const fileFieldsMap = new Map<string, File[]>();

      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          // Handle multiple files for the same field name
          if (!fileFieldsMap.has(key)) {
            fileFieldsMap.set(key, []);
          }
          fileFieldsMap.get(key)!.push(value);
        } else {
          if (!formFieldsMap.has(key)) {
            formFieldsMap.set(key, []);
          }
          formFieldsMap.get(key)!.push(value);
        }
      }

      // Process collected files
      const fileUploadFields = [
        'idPhoto',
        'vanFrontPhoto',
        'vanBackPhoto',
        'vanLeftPhoto',
        'vanRightPhoto',
        'gasSafeCard',
        'insuranceProof',
        'additionalDocuments'
      ];

      console.log("=== FILE PROCESSING START ===");
      console.log("Total file fields found:", fileFieldsMap.size);

      for (const [key, files] of fileFieldsMap.entries()) {
        console.log(`Processing field "${key}":`, files.length, "file(s)");

        if (fileUploadFields.includes(key)) {
          // For file upload fields, keep as array (Zoho expects array format)
          fileFields[key] = files;
          console.log(`✓ "${key}" set as array with ${files.length} file(s)`);

          // Log file details
          files.forEach((file, index) => {
            console.log(`  File ${index + 1}: ${file.name} (${file.size} bytes, ${file.type})`);
          });
        } else {
          // For other file fields, take the first file
          fileFields[key] = files[0];
          console.log(`✓ "${key}" set as single file: ${files[0]?.name || 'undefined'}`);
        }
      }

      console.log("Final fileFields keys:", Object.keys(fileFields));
      console.log("=== FILE PROCESSING END ===");

      // Now process the collected values
      for (const [key, values] of formFieldsMap.entries()) {
        // Handle arrays (like availableDays and preferredWorkType)
        if (key === "availableDays" || key === "preferredWorkType") {
          formFields[key] = values;
        } else {
          // For single values, take the last one (or first if you prefer)
          const value = values[values.length - 1];
          if (value === "true" || value === "false") {
            formFields[key] = value === "true";
          } else if (!isNaN(Number(value)) && value !== "") {
            formFields[key] = Number(value);
          } else {
            formFields[key] = value;
          }
        }
      }

      body = formFields as SubcontractorRequest;
      files = fileFields;

      console.log("Received files:", Object.keys(files));
      console.log("Form data:", body);
    } else {
      // Handle JSON data (no files)
      body = await context.request.body({ type: "json" }).value;
    }

    // Validate required fields
    const requiredFields: (keyof SubcontractorRequest)[] = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!body[field]) {
        context.response.status = 400;
        context.response.body = {
          error: {
            message: `Missing required field: ${field}`,
          },
        };
        return;
      }
    }

    // Create subcontractor service instance
    const subcontractorService = new SubcontractorService();

    // Create Zoho API service instances
    const zohoApi = new ZohoApi(env.ZOHO_API_URL);
    const subcontractorsService = new SubcontractorsService(zohoApi);

    // Send to Zoho CRM with files
    console.log("\n=== CALLING ZOHO SERVICE ===");
    console.log("Body data keys:", Object.keys(body));
    console.log("Files data keys:", Object.keys(files));
    console.log("Files summary:");
    Object.entries(files).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        console.log(`  ${key}: ${value.length} file(s) - ${value.map(f => f.name).join(', ')}`);
      } else {
        console.log(`  ${key}: 1 file - ${value.name}`);
      }
    });

    const zohoResult = await subcontractorsService.createWithFiles(body, files);
    console.log("\n=== ZOHO SERVICE RESULT ===");
    console.log("Zoho integration result:", JSON.stringify(zohoResult, null, 2));

    // Convert files to email attachments
    const attachments = await convertFilesToAttachments(files);

    // Send notification email
    const emailResult = await subcontractorService.sendNotificationEmail({
      to: body.email,
      subcontractorData: body,
      attachments: attachments,
    });

    // Check if there was an error in the email service response
    if (emailResult && 'error' in emailResult) {
      console.warn("Email sending failed:", emailResult.error);
      // Continue processing even if email fails
    }

    // Success response
    context.response.status = 201;
    context.response.body = {
      data: {
        message: "Subcontractor created successfully",
        zoho: zohoResult,
        email: emailResult,
      },
    };

  } catch (error) {
    console.error("Error in create-subcontractor function:", error);

    context.response.status = 500;
    context.response.body = {
      error: {
        message: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
}
