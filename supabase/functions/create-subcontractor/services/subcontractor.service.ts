// supabase/functions/create-subcontractor/services/subcontractor.service.ts

import { EmailService, EmailType } from "../../_shared/email.service.ts";
import { env } from "../../_shared/env.ts";

// Environment variables
// @ts-ignore: Deno is available in Supabase Edge Functions
const SENDGRID_API_KEY = env.SENDGRID_API_KEY;
// @ts-ignore: Deno is available in Supabase Edge Functions
const SENDGRID_SENDER_EMAIL = env.SENDGRID_SENDER_EMAIL;

type EmailAttachment = {
  content: string; // base64 encoded content
  filename: string;
  type?: string; // MIME type
  disposition?: string;
};

type SubcontractorData = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  hearAboutUs?: string;
  contactConsent?: boolean;
  yearsExperience: number;
  travelDistance: number;
  additionalQualifications?: string;
  referred?: string;
  referrerName?: string;

  // Boolean fields
  gasRegistered: boolean;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;

  // Work type and preferences
  workType?: string;
  preferredWorkType?: string | string[];
  availableDays?: string[];
};

type SendNotificationEmailRequest = {
  to: string;
  from?: string;
  subject?: string;
  html?: string;
  emailType?: EmailType;
  attachments?: EmailAttachment[];
  subcontractorData: SubcontractorData;
};

export class SubcontractorService {
  private emailService: EmailService;

  constructor() {
    if (!SENDGRID_API_KEY || !SENDGRID_SENDER_EMAIL) {
      throw new Error("Missing required environment variables: SENDGRID_API_KEY or SENDGRID_SENDER_EMAIL");
    }
    this.emailService = new EmailService(SENDGRID_API_KEY, SENDGRID_SENDER_EMAIL);
  }

  /**
   * Send notification email to subcontractor
   */
  async sendNotificationEmail(request: SendNotificationEmailRequest) {
    try {
      // Generate custom HTML content for subcontractor welcome email
      const html = request.html || this.generateWelcomeEmailHtml(request.subcontractorData);
      
      // Set default email type if not provided
      const emailType = EmailType.SUBCONTRACTOR_CREATED;

      // Send email with attachments and HTML content
      const result = await this.emailService.sendEmail({
        to: SENDGRID_SENDER_EMAIL, 
        from: SENDGRID_SENDER_EMAIL,
        emailType: emailType,
        html: html,
        attachments: request.attachments,
      });

      return result;
    } catch (error) {
      console.error("Error sending notification email:", error);
      return {
        error: {
          message: "Failed to send notification email",
          details: error.message,
        },
      };
    }
  }

  /**
   * Generate welcome email HTML content
   */
  private generateWelcomeEmailHtml(data: SubcontractorData): string {
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .section { background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0; }
            .section h3 { margin-top: 0; color: #007bff; }
            .field-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
            .field { margin: 5px 0; }
            .field strong { color: #333; }
            .yes { color: #28a745; font-weight: bold; }
            .no { color: #dc3545; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Subcontractor Application</h1>
              <p>Pleasant Plumbers - Subcontractor Registration</p>
            </div>
            <div class="content">

              <div class="section">
                <h3>📋 Personal Information</h3>
                <div class="field-grid">
                  <div class="field"><strong>Name:</strong> ${data.firstName} ${data.lastName}</div>
                  <div class="field"><strong>Email:</strong> ${data.email}</div>
                  <div class="field"><strong>Mobile:</strong> ${data.mobile}</div>
                  <div class="field"><strong>Date of Birth:</strong> ${data.dateOfBirth}</div>
                </div>
              </div>

              <div class="section">
                <h3>🏠 Address Information</h3>
                <div class="field"><strong>Address Line 1:</strong> ${data.addressLine1}</div>
                ${data.addressLine2 ? `<div class="field"><strong>Address Line 2:</strong> ${data.addressLine2}</div>` : ''}
                <div class="field"><strong>Post Code:</strong> ${data.postCode}</div>
              </div>

              <div class="section">
                <h3>💼 Professional Details</h3>
                <div class="field-grid">
                  <div class="field"><strong>Years Experience:</strong> ${data.yearsExperience} years</div>
                  <div class="field"><strong>Travel Distance:</strong> ${data.travelDistance} miles</div>
                  <div class="field"><strong>Work Type:</strong> ${data.workType || 'Not specified'}</div>
                  <div class="field"><strong>Preferred Work Type:</strong> ${Array.isArray(data.preferredWorkType) ? data.preferredWorkType.join(', ') : (data.preferredWorkType || 'Not specified')}</div>
                </div>
                ${data.additionalQualifications ? `<div class="field"><strong>Additional Qualifications:</strong> ${data.additionalQualifications}</div>` : ''}
              </div>

              <div class="section">
                <h3>✅ Compliance & Certifications</h3>
                <div class="field-grid">
                  <div class="field"><strong>Gas Registered:</strong> <span class="${data.gasRegistered ? 'yes' : 'no'}">${data.gasRegistered ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Driving License:</strong> <span class="${data.drivingLicense ? 'yes' : 'no'}">${data.drivingLicense ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Public Liability Insurance:</strong> <span class="${data.publicLiabilityInsurance ? 'yes' : 'no'}">${data.publicLiabilityInsurance ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Contact Consent:</strong> <span class="${data.contactConsent ? 'yes' : 'no'}">${data.contactConsent ? 'Yes' : 'No'}</span></div>
                </div>
              </div>

              <div class="section">
                <h3>🚐 Equipment & Resources</h3>
                <div class="field-grid">
                  <div class="field"><strong>Has Own Van:</strong> <span class="${data.hasOwnVan ? 'yes' : 'no'}">${data.hasOwnVan ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Has Own Tools:</strong> <span class="${data.hasOwnTools ? 'yes' : 'no'}">${data.hasOwnTools ? 'Yes' : 'No'}</span></div>
                </div>
              </div>

              <div class="section">
                <h3>📍 Location & Availability</h3>
                <div class="field-grid">
                  <div class="field"><strong>Central London:</strong> <span class="${data.centralLondon ? 'yes' : 'no'}">${data.centralLondon ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Out of Hours Work:</strong> <span class="${data.outOfHoursWork ? 'yes' : 'no'}">${data.outOfHoursWork ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Emergency Callouts:</strong> <span class="${data.emergencyCallouts ? 'yes' : 'no'}">${data.emergencyCallouts ? 'Yes' : 'No'}</span></div>
                  <div class="field"><strong>Accepted Rates:</strong> <span class="${data.acceptedRates ? 'yes' : 'no'}">${data.acceptedRates ? 'Yes' : 'No'}</span></div>
                </div>
                ${data.availableDays && data.availableDays.length > 0 ? `<div class="field"><strong>Available Days:</strong> ${data.availableDays.join(', ')}</div>` : ''}
              </div>

              ${data.hearAboutUs || data.referred || data.referrerName ? `
              <div class="section">
                <h3>📢 How They Found Us</h3>
                ${data.hearAboutUs ? `<div class="field"><strong>Heard About Us:</strong> ${data.hearAboutUs}</div>` : ''}
                ${data.referred ? `<div class="field"><strong>Referred:</strong> ${data.referred}</div>` : ''}
                ${data.referrerName ? `<div class="field"><strong>Referrer Name:</strong> ${data.referrerName}</div>` : ''}
              </div>
              ` : ''}

              <div class="section">
                <h3>📅 Application Details</h3>
                <div class="field"><strong>Submitted:</strong> ${new Date().toLocaleString()}</div>
                <div class="field"><strong>Status:</strong> New Application - Pending Review</div>
              </div>

            </div>
            <div class="footer">
              <p>This application has been automatically submitted to Pleasant Plumbers.</p>
              <p>Please review and follow up with the applicant as needed.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Validate subcontractor data
   */
  validateSubcontractorData(data: SubcontractorData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields validation
    const requiredFields = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!data[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Email validation
    if (data.email && !this.isValidEmail(data.email)) {
      errors.push("Invalid email format");
    }

    // Mobile validation
    if (data.mobile && !this.isValidMobile(data.mobile)) {
      errors.push("Invalid mobile number format");
    }

    // Years of experience validation
    if (data.yearsExperience !== undefined && (data.yearsExperience < 0 || data.yearsExperience > 50)) {
      errors.push("Years of experience must be between 0 and 50");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate mobile number format
   */
  private isValidMobile(mobile: string): boolean {
    // Basic UK mobile number validation
    const mobileRegex = /^(\+44|0)[0-9]{10}$/;
    return mobileRegex.test(mobile.replace(/\s/g, ''));
  }
}
