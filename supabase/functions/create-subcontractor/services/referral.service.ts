import { EmailService, EmailType } from "../../_shared/email.service.ts";
import { env } from "../../_shared/env.ts";

// Environment variables
const SENDGRID_API_KEY = env.SENDGRID_API_KEY;
const SENDGRID_SENDER_EMAIL = env.SENDGRID_SENDER_EMAIL;


export class ReferralService {
  private emailService: EmailService;

  constructor() {
    if (!SENDGRID_API_KEY || !SENDGRID_SENDER_EMAIL) {
      throw new Error("Missing required environment variables: SENDGRID_API_KEY or SENDGRID_SENDER_EMAIL");
    }
    this.emailService = new EmailService(SENDGRID_API_KEY, SENDGRID_SENDER_EMAIL);
  }

  /**
   * Send referral info pack notification to admin
   */
  async sendReferralInfoPack(userEmail: string) {
    try {
      // Send notification to admin
      // const adminEmail = "<EMAIL>";
      const adminEmail = SENDGRID_SENDER_EMAIL;
      const html = this.generateAdminNotificationHtml(userEmail);

      // Set default email type if not provided
      const emailType = EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED;

      // Send email to admin
      const result = await this.emailService.sendEmail({
        to: adminEmail,
        subject: "Subcontractor Info Pack Request",
        emailType: emailType,
        html: html,
      });

      return result;
    } catch (error) {
      console.error("Error sending referral info pack notification:", error);
      return {
        error: {
          message: "Failed to send referral info pack notification",
          details: error.message,
        },
      };
    }
  }

  /**
   * Generate admin notification email HTML content
   */
  private generateAdminNotificationHtml(userEmail: string): string {
    return `
      <html>
        <head>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              margin: 0;
              padding: 20px;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background-color: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
              background-color: #007bff;
              color: white;
              padding: 20px;
              text-align: center;
              border-radius: 8px 8px 0 0;
              margin: -20px -20px 20px -20px;
            }
            .content {
              padding: 20px 0;
            }
            .email-highlight {
              background-color: #f8f9fa;
              padding: 15px;
              border-left: 4px solid #007bff;
              margin: 15px 0;
              font-size: 16px;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Subcontractor Info Pack Request</h2>
            </div>

            <div class="content">
              <p>Hello Admin,</p>

              <p>Someone has requested the subcontractor info pack from the website.</p>

              <div class="email-highlight">
                📧 Email: ${userEmail}
              </div>

              <p><strong>Action Required:</strong> Please send the subcontractor info pack to the above email address.</p>

              <p>This notification was generated automatically from the Pleasant Plumbers website.</p>

              <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">

              <p style="font-size: 12px; color: #666;">
                <strong>Pleasant Plumbers</strong><br>
                Timestamp: ${new Date().toLocaleString()}<br>
                Source: Website Referral Form
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate referral info pack request
   */
  validateReferralRequest(email: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Email validation
    if (!email) {
      errors.push("Email is required");
    } else if (!this.isValidEmail(email)) {
      errors.push("Invalid email format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
