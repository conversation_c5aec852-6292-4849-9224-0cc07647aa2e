import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const card = style({
  backgroundColor: "#00E676",
  borderRadius: "16px",
  overflow: "visible", // Allow image to extend outside
  display: "flex",
  flexDirection: "column-reverse", // Reversed for mobile - content first, then image
  position: "relative",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row", // Original layout for tablet and up
      alignItems: "center",
      height: "320px",
    }
  }
});

export const imageWrapper = style({
  width: "100%",
  position: "relative",
  overflow: "visible",
  marginTop: "-25px",
  display: "flex",
  justifyContent: "center",
  alignItems: "flex-end",

  "@media": {
    [breakpoints.tablet]: {
      width: "35%",
      maxWidth: "400px",
      marginTop: "-40px",
      justifyContent: "flex-start",
    }
  }
});

export const image = style({
  // width: "350px",
  maxWidth: "350px",
  height: "auto",
  display: "block",
  zIndex: 3,
  position: "relative",
  top: "0", // No top offset needed for bottom position
  // objectFit: "contain",
  marginBottom: "0", // No margin needed

  "@media": {
    [breakpoints.tablet]: {
      width: "100%",
      maxWidth: "none",
      objectFit: "cover",
      marginBottom: 0,
      top: "-20px", // Original top offset for tablet and up
    }
  }
});

export const contentWrapper = style({
  padding: "32px 24px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center", // Center content on mobile
  justifyContent: "center",
  textAlign: "center", // Center text on mobile

  "@media": {
    [breakpoints.tablet]: {
      padding: "40px 48px",
      flex: 1,
      alignItems: "flex-start", // Left align on tablet
      textAlign: "left", // Left align text on tablet
    },
    [breakpoints.desktop]: {
      padding: "60px 80px",
      alignItems: "flex-start",
    }
  }
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontStyle: "normal",
  fontWeight: 400,
  lineHeight: "95%",
  letterSpacing: "-1.28px",
  color: theme.colors.primary.castletonGreen,
  fontSize: "44px",
  marginBottom: "32px",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      // fontSize: "36px",
      marginBottom: "40px",
      textAlign: "left",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
    }
  }
});

export const titleHighlight = style({
  fontStyle: "italic",
  display: "inline",
  fontWeight: 600,
});

export const breakLine = style({
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  }
});

export const button = style({
  position: "relative",
  height: "56px",
  backgroundColor: theme.colors.primary.ivory,
  ":hover": {
    backgroundColor: theme.colors.primary.softWhite,
  },
  "@media": {
    [breakpoints.tablet]: {
      right: "90px"
    },
    [breakpoints.desktop]: {
      right: "125px",
    }
  }
});

export const mobileTitle = style({
  display: "block",
  
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  }
});

export const desktopTitle = style({
  display: "none",
  lineHeight: "95%",
  letterSpacing: "-1.28px",
  
  "@media": {
    [breakpoints.tablet]: {
      display: "block",
    },
    [breakpoints.desktop]: {
      fontSize: "54px"
    }
  }
});
