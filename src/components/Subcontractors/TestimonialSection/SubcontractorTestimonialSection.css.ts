import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const section = style({
  // padding: "40px 0",
  display: "flex",
  fontFamily: theme.fonts.secondary,
  flexDirection: "column",
  gap: "20px",

  "@media": {
    [breakpoints.tablet]: {
      // padding: "60px 0",
      gap: "30px",
    },
    [breakpoints.desktop]: {
      // padding: "80px 0",
      gap: "40px",
    }
  }
});

export const testimonialCard = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "24px",
  padding: "24px",
  color: theme.colors.primary.ivory,
  position: "relative",
  overflow: "hidden",
  display: "flex",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      padding: "32px",
      flexDirection: "row",
    },
    [breakpoints.desktop]: {
      padding: "15px 45px 40px 45px",
    }
  }
});

export const logoWrapper = style({
  marginBottom: "20px",
  margin: "0 auto",
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "initial",
      margin: 0,
      position: "absolute",
      top: "32px",
      left: "32px",
    },
    [breakpoints.desktop]: {
      top: "40px",
      left: "40px",
    }
  }
});

export const logo = style({
  width: "240px",
  height: "110px",
});

export const testimonialContent = style({
  display: "flex",
  flexDirection: "column",
  zIndex: 1,

  "@media": {
    [breakpoints.tablet]: {
      flex: "1 1 60%",
      paddingTop: "60px",
      paddingRight: "20px",
    },
    [breakpoints.desktop]: {
      paddingTop: "80px",
      paddingRight: "40px",
      width: "774px"
    }
  }
});

export const testimonialText = style({
  fontSize: "20px",
  maxWidth: "774px",
  fontWeight: 400,
  lineHeight: 1.2,
  // marginTop: "60px",
  fontFamily: theme.fonts.secondary,

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "100px",
    },
    [breakpoints.desktop]: {
      fontSize: "40px",
      textAlign: "left",
    }
  }
});

export const testimonialAuthor = style({
  marginLeft: "auto"
});

export const authorName = style({
  fontSize: "20px",
  marginTop: "40px",
  fontFamily: theme.fonts.primary,
  lineHeight: "95%",
  letterSpacing: "-0.4px",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: "36px",
    }

  }
});

export const authorNameHighlight = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: "italic",
  fontWeight: 600,
});

export const testimonialImageWrapper = style({
  alignSelf: "center",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: 0,
      flex: "1 1 40%",
      alignSelf: "flex-end",
      display: "flex",
      justifyContent: "flex-end",
    }
  }
});

export const testimonialImage = style({
  maxWidth: "105%",
  height: "auto",
  right: "10px",
  borderRadius: "8px",
  position: "relative",
  top: "28px",

  "@media": {
    [breakpoints.tablet]: {
      maxHeight: "400px",
      objectFit: "contain",
      objectPosition: "bottom right",
      top: "40px",
    },
    [breakpoints.desktop]: {
      maxHeight: "500px",
    }
  }
});

export const statsContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: "20px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: "30px",
    }
  }
});

export const statsCard = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: "16px",
  padding: "24px",
  color: "white",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  textAlign: "center",
  lineHeight: 1.2,
  fontFamily: "Helvetica Neue",

  "@media": {
    [breakpoints.tablet]: {
      flex: 1,
      padding: "32px",
    },
    [breakpoints.desktop]: {
      padding: "40px",
      width: "332px",
    }
  }
});

export const statsTitle = style({
  fontFamily: theme.fonts.secondary,
  marginBottom: "60px",
  color: theme.colors.primary.ivory,
  fontSize: "24px",
  lineHeight: "120%",
  letterSpacing: "-0.24px"
});

export const statsHighlight = style({
  color: theme.colors.primary.asidGreen,
  fontFamily: theme.fonts.primary,
  fontSize: "40px",
  fontWeight: 600,
  fontStyle: "italic",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
    },
    [breakpoints.desktop]: {
      fontSize: "80px",
    }
  }
});

export const fast = style({
  fontStyle: "italic",
  fontWeight: 600,
});

export const statsSubtitle = style({
  marginTop: "60px",
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.ivory,
  fontSize: "24px",
  "@media": {
    [breakpoints.tablet]: {
      marginTop: "76px",
    }
  }
});

export const vacanciesCard = style({
  backgroundColor: "#FFF",
  borderRadius: "16px",
  padding: "24px",
  color: theme.colors.primary.castletonGreen,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      flex: 1,
      padding: "32px",
    },
    [breakpoints.desktop]: {
      padding: "40px",
    }
  }
});

export const vacanciesLogoWrapper = style({
  marginBottom: "45px",
});

export const vacanciesLogo = style({
  width: "172px",
  height: "79px",
});

export const vacanciesTitle = style({
  fontSize: "40px",
  fontWeight: 400,
  marginBottom: "40px",
  fontFamily: theme.fonts.primary,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "56px",
    },
    [breakpoints.desktop]: {
      fontSize: "80px",
      lineHeight: "95%",
      letterSpacing: "-1.6px"
    }
  }
});

export const vacanciesText = style({
  fontSize: "24px",
  lineHeight: 1.2,
  fontFamily: theme.fonts.secondary,
  fontWeight: 500,
});

export const ctaContainer = style({
  display: "flex",
  justifyContent: "center",
  marginBottom: "40px",

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: "76px",
    },
  }
});

export const ctaButton = style({
  // backgroundColor: theme.colors.primary.asidGreen,
  width: "100%",
  // color: theme.colors.primary.castletonGreen,
  fontWeight: 600,
  fontFamily: theme.fonts.secondary,
  borderRadius: "100px",
  // padding: "12px 24px",
  fontSize: "20px",
  // border: "none",
  // cursor: "pointer",
  // transition: "background-color 0.2s ease",
  height: "56px",

  "@media": {
    [breakpoints.tablet]: {
      width: "250px",
      padding: "14px 32px",
      fontSize: "16px",
    },
    [breakpoints.desktop]: {
      width: "250px",
      padding: "16px 40px",
      fontSize: "20px",
    }
  },
});
