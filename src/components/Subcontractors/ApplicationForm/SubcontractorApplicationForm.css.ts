import { style } from "@vanilla-extract/css";
import { breakpoints, breakpointValues } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";

export const formContainer = style({
  width: "100%",
  minHeight: "100vh",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  padding: "20px 10px",
  // backgroundColor: theme.colors.primary.ivory,
});

export const formWrapper = style({
  width: "100%",
  // maxWidth: "1200px",
  backgroundColor: theme.colors.primary.ivory,
  borderRadius: "12px",
  overflow: "hidden",
  boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
  padding: "15px",
  
  "@media": {
    [breakpoints.tablet]: {
      padding: "20px",
    }
  }
});

export const formContent = style({
  display: "flex",
  flexDirection: "column",
  width: "100%",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "column",
      gap: "20px",
    },
    [breakpoints.desktop]: {
      flexDirection: "row",
    },
  }
});

export const stepContainer = style({
  flex: 1,
  padding: "20px",
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "24px",
  width: "100%",
  overflow: "auto",
});
