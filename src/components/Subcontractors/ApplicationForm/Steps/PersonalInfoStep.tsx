import React from "react";
import * as styles from "./Steps.css";
import {FormData} from "../SubcontractorApplicationForm";
import Button from "@components/Button";
import Image from "next/image";
import searchIcon from "../../search.svg";
import {useSearchParams} from "next/navigation";
import {Controller, useForm} from "react-hook-form";
import {EMAIL_CONTROLLER_RULES} from "@/utils/constants";

interface PersonalInfoStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  onNext: () => void;
}

const PersonalInfoStep: React.FC<PersonalInfoStepProps> = ({
  formData,
  updateFormData,
  onNext
}) => {

  const searchParams = useSearchParams();

  const onBack = () => {
    try {
      const mode = searchParams?.get('mode') || 'residential';
      console.log("Mode:", mode);
      window.location.href = `/subcontractors${mode !== 'residential' ? `?mode=${mode}` : ''}`;
    } catch (error) {
      console.error("Error in onBack:", error);
      window.location.href = '/subcontractors';
    }
  };

  const {
    register,
    handleSubmit,
    control,
    trigger,
    getValues,
    formState: { errors }
  } = useForm({
    defaultValues: formData,
    reValidateMode: "onBlur"
  });

  const handleNextClick = async () => {
    const isValid = await trigger();
    if (isValid) {
      updateFormData(getValues());
      onNext();
    }
  };

  return (
      <div className={styles.stepWrapper}>
        <div className={styles.stepHeader}>
          <div className={styles.stepIndicator}>Step 1/4</div>
          <h2 className={styles.stepTitle}>Tell Us About You</h2>
          <p className={styles.stepDescription}>
            If you have any questions, save them until the end of this form and our team will
            answer everything for you
          </p>
        </div>

        <div className={styles.form}>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="firstName" className={styles.label}>
                First name <span className={styles.required}>*</span>
              </label>
              <input
                  id="firstName"
                  className={styles.input}
                  placeholder="Enter your first name"
                  {...register("firstName", {
                    required: "First name is required",
                    minLength: {
                      value: 2,
                      message: "First name must be at least 2 characters"
                    },
                    pattern: {
                      value: /^[A-Za-z\s-']+$/,
                      message: "Please enter a valid name"
                    }
                  })}
              />
              {errors.firstName && <p className={styles.errorText}>{errors.firstName.message}</p>}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="lastName" className={styles.label}>
                Last name <span className={styles.required}>*</span>
              </label>
              <input
                  id="lastName"
                  className={styles.input}
                  placeholder="Enter your last name"
                  {...register("lastName", {
                    required: "Last name is required",
                    minLength: {
                      value: 2,
                      message: "Last name must be at least 2 characters"
                    },
                    pattern: {
                      value: /^[A-Za-z\s-']+$/,
                      message: "Please enter a valid name"
                    }
                  })}
              />
              {errors.lastName && <p className={styles.errorText}>{errors.lastName.message}</p>}
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="mobile" className={styles.label}>
                Mobile number <span className={styles.required}>*</span>
              </label>
              <input
                  id="mobile"
                  type="tel"
                  inputMode="tel"
                  className={styles.input}
                  placeholder="+XX XXX XXX XX XX"
                  maxLength={16}
                  {...register("mobile", {
                    required: "Mobile number is required",
                    validate: (value) => {
                      // Check if the number starts with +
                      if (!value.startsWith('+')) {
                        return "Phone number must start with + symbol";
                      }

                      // Remove all non-digit characters except + for validation
                      const cleanedValue = value.replace(/[^\d+]/g, '');

                      // Check if there's at least one digit after +
                      if (cleanedValue.length < 2) {
                        return "Please enter digits after +";
                      }

                      // Check that the total length is reasonable
                      if (cleanedValue.length < 8) {
                        return "Phone number is too short";
                      }

                      if (cleanedValue.length > 16) {
                        return "Phone number is too long";
                      }

                      return true;
                    }
                  })}
                  onChange={(e) => {
                    // Only allow + and digits, no spaces or other formatting
                    const value = e.target.value;
                    let formatted = value.replace(/[^\d+]/g, '');

                    // Ensure it starts with + if user is typing
                    if (formatted && !formatted.startsWith('+')) {
                      formatted = '+' + formatted;
                    }

                    e.target.value = formatted;
                  }}
              />
              {errors.mobile && <p className={styles.errorText}>{errors.mobile.message}</p>}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="email" className={styles.label}>
                Email <span className={styles.required}>*</span>
              </label>
              <input
                  id="email"
                  type="email"
                  className={styles.input}
                  placeholder="<EMAIL>"
                  {...register("email", {
                    required: "Email is required",
                    ...EMAIL_CONTROLLER_RULES
                  })}
              />
              {errors.email && <p className={styles.errorText}>{errors.email.message}</p>}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="addressLine1" className={styles.label}>
              Address line 1 <span className={styles.required}>*</span>
            </label>
            <input
                id="addressLine1"
                type="text"
                className={styles.input}
                placeholder="Apartment, suite, etc."
                {...register("addressLine1", {
                  required: "Address is required",
                  minLength: {
                    value: 3,
                    message: "Address must be at least 3 characters"
                  }
                })}
            />
            {errors.addressLine1 && <p className={styles.errorText}>{errors.addressLine1.message}</p>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="addressLine2" className={styles.label}>
              Address line 2 (optional)
            </label>
            <input
                id="addressLine2"
                type="text"
                className={styles.input}
                placeholder="Optional address line"
                {...register("addressLine2")}
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="postCode" className={styles.label}>
                Postcode <span className={styles.required}>*</span>
              </label>
              <div className={styles.searchInputWrapper}>
                <input
                    id="postCode"
                    type="text"
                    maxLength={10}
                    className={styles.input}
                    placeholder="Enter your post code"
                    {...register("postCode", {
                      required: "Post code is required",
                      validate: (value) => {
                        if (!value.trim()) {
                          return "Post code is required";
                        }

                        // Simple validation - just check for minimum length
                        if (value.trim().length >= 2) {
                          return true;
                        }

                        return "Please enter a valid post code";
                      }
                    })}
                />
                <button type="button" className={styles.searchButton}>
                  <Image alt="search icon" src={searchIcon} className={styles.searchIcon} />
                </button>
              </div>
              {errors.postCode && <p className={styles.errorText}>{errors.postCode.message}</p>}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="dateOfBirth" className={styles.label}>
                Date of birth <span className={styles.required}>*</span>
              </label>
              <Controller
                  name="dateOfBirth"
                  control={control}
                  rules={{
                    required: "Date of birth is required",
                    validate: (value) => {
                      if (!value) return "Date of birth is required";
                      const [day, month, year] = value.split('/').map(Number);
                      const date = new Date(year, month - 1, day);
                      const isValidDate = date.getDate() === day &&
                          date.getMonth() === month - 1 &&
                          date.getFullYear() === year;

                      if (!isValidDate) return "Please enter a valid date";
                      const today = new Date();
                      const eighteenYearsAgo = new Date(
                          today.getFullYear() - 18,
                          today.getMonth(),
                          today.getDate()
                      );

                      if (date > eighteenYearsAgo) {
                        return "You must be at least 18 years old";
                      }

                      return true;
                    }
                  }}
                  render={({ field: { ref, ...restField }, fieldState }) => (
                      <input
                          id="dateOfBirth"
                          type="text"
                          className={styles.input}
                          placeholder="DD/MM/YYYY"
                          maxLength={10}
                          ref={ref}
                          {...restField}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^\d]/g, '');
                            let formatted = '';

                            if (value.length > 0) {
                              formatted = value.substring(0, 2);
                              if (value.length > 2) {
                                formatted += '/' + value.substring(2, 4);
                                if (value.length > 4) {
                                  formatted += '/' + value.substring(4, 8);
                                }
                              }
                            }

                            restField.onChange(formatted);
                          }}
                      />
                  )}
              />
              {errors.dateOfBirth && <p className={styles.errorText}>{errors.dateOfBirth.message}</p>}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="hearAboutUs" className={styles.label}>
              How did you hear about us? <span className={styles.required}>*</span>
            </label>
            <select
                id="hearAboutUs"
                className={styles.select}
                {...register("hearAboutUs", {
                  required: "Please select an option"
                })}
            >
              <option value="" disabled>Select</option>
              <option value="Google">Google</option>
              <option value="Facebook">Facebook</option>
              <option value="Instagram">Instagram</option>
              <option value="Friend">Friend or colleague</option>
              <option value="Other">Other</option>
            </select>
            {errors.hearAboutUs && <p className={styles.errorText}>{errors.hearAboutUs.message}</p>}
          </div>
  
          <div className={styles.formGroup}>
          <label className={styles.label}>Were you referred by someone? <span className={styles.required}>*</span></label> 

          <div className={styles.radioGroup}> 
            <div className={styles.radioOption}>
              <input
                id="referredYes" 
                type="radio"
                value="yes"
                {...register("referred", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="referredYes" className={styles.radioLabel}>Yes</label>
            </div>

            <div className={styles.radioOption}>
              <input
                id="referredNo" 
                type="radio"
                value="no"
                {...register("referred", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="referredNo" className={styles.radioLabel}>No</label> 
            </div>
          </div>
          {errors.referred && <p className={styles.errorText}>{errors.referred.message}</p>}
        </div>

        {/* Поле "Name of your referrer" тепер завжди видиме */}
        <div className={styles.formGroup}>
          <label htmlFor="referrerName" className={styles.label}>
            Name of your referrer
          </label>
          <input
            id="referrerName"
            className={styles.input}
            placeholder="Full Name"
            {...register("referrerName")}
          />
          {errors.referrerName && <p className={styles.errorText}>{errors.referrerName.message}</p>}
        </div>

          <div className={styles.formGroup}>
            <div className={styles.checkboxWrapper}>
              <input
                  id="contactConsent"
                  type="checkbox"
                  className={styles.checkbox}
                  {...register("contactConsent", {
                    required: "You must agree to be contacted"
                  })}
              />
              <label htmlFor="contactConsent" className={styles.checkboxLabel}>
                I agree to Pleasant Plumbers contacting me regarding jobs and onboarding
              </label>
            </div>
            {errors.contactConsent && <p className={styles.errorText}>{errors.contactConsent.message}</p>}
          </div>

          <div className={styles.buttonGroup}>
            <Button
                type="button"
                variant="outlined"
                color="secondary"
                className={styles.backButton}
                onClick={onBack}
            >
              ← Back
            </Button>
            <Button
                type="button"
                className={styles.nextButton}
                onClick={handleNextClick}
            >
              Job Preferences →
            </Button>
          </div>
        </div>
      </div>
  );
};

export default PersonalInfoStep;


